# Authentication Data Access Layer (DAL)

This directory implements route-level authorization for authentication pages using the Data Access Layer (DAL) pattern as recommended in the Next.js documentation.

## Overview

The DAL pattern centralizes authentication and authorization logic, providing a clean separation between data access and presentation layers. This implementation protects sign-up and sign-in pages by redirecting already-authenticated users to appropriate destinations based on their status.

## Architecture

### Files Structure

```
apps/dashboard/src/features/auth/dal/
├── auth-dal.ts                 # Main DAL implementation
├── README.md                   # This documentation
```

### Components

```
apps/dashboard/src/features/auth/components/
├── auth-page-guard.tsx         # Route protection wrapper
```

## Implementation Details

### AuthDAL Class

The `AuthDAL` class provides centralized authentication logic:

#### Key Methods

- **`getSessionData()`**: Retrieves complete session data including user info, organizations, and active organization
- **`checkAuthPageAccess()`**: Determines if authenticated users should be redirected from auth pages
- **`determineRedirectDestination()`**: Implements priority-based redirect logic
- **Utility methods**: `requiresEmailVerification()`, `hasCompletedOnboarding()`, `getOrganizationCount()`

#### Data Transfer Objects (DTOs)

- **`AuthUserDTO`**: Minimal user data for authorization decisions
- **`OrganizationDTO`**: Essential organization information
- **`AuthSessionDTO`**: Complete session context
- **`AuthorizationResult`**: Authorization decision result

### AuthPageGuard Component

Server component that wraps authentication pages and applies route protection:

```tsx
export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
```

## Authorization Logic Flow

When a user attempts to access sign-up or sign-in pages:

1. **Check Authentication**: Validate if user has a valid session
2. **If Not Authenticated**: Allow access to auth page
3. **If Authenticated**: Redirect based on priority:

### Redirect Priority (in order)

1. **Incomplete Onboarding** → `/onboarding/workspace`
   - `session.user.onboardingStatus !== "complete"`

2. **No Organizations** → `/onboarding/workspace`
   - User has no organization memberships

3. **Default Workspace** → `/{session.user.defaultWorkspace}/home`
   - User has a configured default workspace

4. **Active Organization** → `/{activeOrganization.slug}/home`
   - User has an active organization in their session

5. **First Organization** → `/{firstOrganization.slug}/home`
   - Use oldest organization (ordered by createdAt)

6. **Email Verification** → `/verify`
   - `!session.user.emailVerified`

7. **Fallback** → `/`
   - Default redirect to root

## Key Features

### Performance Optimizations

- **React Cache**: Uses `cache()` to prevent duplicate API calls within the same request
- **Promise.allSettled**: Parallel API calls for organizations and active organization
- **Graceful Degradation**: Continues operation even if organization APIs fail

### Error Handling

- **Non-blocking Failures**: Organization API failures don't prevent session validation
- **Comprehensive Logging**: Detailed error logging for debugging
- **Fallback Behavior**: Safe defaults when data is unavailable

### Security Considerations

- **Minimal Data Exposure**: DTOs contain only necessary data for authorization
- **Server-side Validation**: All authorization logic runs on the server
- **Session Validation**: Uses existing `getBackendSession` utility for consistency

## Usage Examples

### Protecting Authentication Pages

```tsx
// apps/dashboard/src/app/(auth)/sign-in/page.tsx
import { AuthPageGuard } from "@/src/features/auth/components/auth-page-guard";

export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
```

### Using DAL Utilities

```tsx
// Check if user needs email verification
const needsVerification = await AuthDAL.requiresEmailVerification();

// Check onboarding status
const hasCompleted = await AuthDAL.hasCompletedOnboarding();

// Get organization count
const orgCount = await AuthDAL.getOrganizationCount();
```

## Integration Points

### Existing Systems

- **Session Management**: Uses `getBackendSession` from `@/src/features/auth/utils/session`
- **API Layer**: Integrates with `getUserOrganizations` and `getActiveOrganization`
- **Backend Validation**: Compatible with Hono backend session middleware

### Next.js Features

- **Server Components**: Runs authorization logic on the server
- **Redirect**: Uses Next.js `redirect()` for navigation
- **Caching**: Leverages React `cache()` for performance

## Maintenance

### Adding New Redirect Rules

1. Update `determineRedirectDestination()` method
2. Update this documentation

### Modifying DTOs

1. Update interface definitions in `auth-dal.ts`
2. Update transformation logic in `getSessionData()`

### Performance Monitoring

Monitor the following metrics:

- Session validation response times
- Organization API call success rates
- Cache hit rates for session data
- Redirect accuracy and user experience

## Dependencies

- `next/navigation`: For server-side redirects
- `react`: For caching functionality
- `@/src/features/auth/utils/session`: Session validation
- `@/src/lib/api/onboarding`: Organization data APIs

This implementation provides a robust, scalable, and maintainable solution for route-level authorization that follows Next.js best practices and integrates seamlessly with the existing authentication system.
