import { cache } from "react";
import { getBackendSession } from "@/src/features/auth/utils/session";
import {
  getActiveOrganization,
  getUserOrganizations
} from "@/src/lib/api/onboarding";

/**
 * Data Transfer Objects (DTOs) for session data
 * These interfaces define the minimal data needed for authorization decisions
 */
export interface AuthUserDTO {
  id: string;
  email: string;
  name: string;
  emailVerified: boolean;
  onboardingStatus: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace: string | null;
}

export interface OrganizationDTO {
  id: string;
  name: string;
  slug: string;
  userRole: "viewer" | "contributor" | "admin" | "owner";
}

export interface AuthSessionDTO {
  user: AuthUserDTO;
  organizations: OrganizationDTO[];
  activeOrganization: OrganizationDTO | null;
}

/**
 * Authorization result indicating what action should be taken
 */
export interface AuthorizationResult {
  isAuthenticated: boolean;
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
}

/**
 * Data Access Layer for Authentication
 * Centralizes all authentication and authorization logic
 */
export class AuthDAL {
  /**
   * Get current session with all necessary data for authorization decisions
   * Uses React cache to prevent duplicate API calls within the same request
   */
  static getSessionData = cache(async (): Promise<AuthSessionDTO | null> => {
    try {
      // Get basic session data
      const session = await getBackendSession();
      if (!session?.user) {
        return null;
      }

      // Transform session user to DTO format
      const user: AuthUserDTO = {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        emailVerified: !!session.user.emailVerified,
        onboardingStatus: session.user.onboardingStatus || "incomplete",
        defaultWorkspace: session.user.defaultWorkspace || null
      };

      // Initialize with empty data - we'll fetch organizations separately if needed
      const organizations: OrganizationDTO[] = [];
      const activeOrganization: OrganizationDTO | null = null;

      return {
        user,
        organizations,
        activeOrganization
      };
    } catch (error) {
      console.error("Error in AuthDAL.getSessionData:", error);
      return null;
    }
  });

  /**
   * Get session data with organization information
   * Only fetches organization data when explicitly needed and user is authenticated
   */
  static getSessionDataWithOrganizations = cache(
    async (): Promise<AuthSessionDTO | null> => {
      try {
        // First get basic session data
        const sessionData = await this.getSessionData();
        if (!sessionData) {
          return null;
        }

        // Only fetch organization data if user is authenticated and has completed basic onboarding
        if (sessionData.user.id && sessionData.user.emailVerified) {
          try {
            // Fetch organizations data with proper error handling
            const [orgsResponse, activeOrgResponse] = await Promise.allSettled([
              getUserOrganizations(),
              getActiveOrganization()
            ]);

            if (
              orgsResponse.status === "fulfilled" &&
              orgsResponse.value?.organizations
            ) {
              sessionData.organizations = orgsResponse.value.organizations.map(
                (org: {
                  id: string;
                  name: string;
                  slug: string;
                  userRole: string;
                }) => ({
                  id: org.id,
                  name: org.name,
                  slug: org.slug,
                  userRole: org.userRole
                })
              );
            } else if (orgsResponse.status === "rejected") {
              // Don't log 401 errors as warnings since they're expected for some auth states
              const error = orgsResponse.reason;
              if (error?.message?.includes("401")) {
                console.debug(
                  "User not authorized to fetch organizations (expected during auth flow)"
                );
              } else {
                console.warn("Failed to fetch user organizations:", error);
              }
            }

            if (
              activeOrgResponse.status === "fulfilled" &&
              activeOrgResponse.value?.activeOrganization
            ) {
              const activeOrg = activeOrgResponse.value.activeOrganization;
              sessionData.activeOrganization = {
                id: activeOrg.id,
                name: activeOrg.name,
                slug: activeOrg.slug,
                userRole: activeOrg.userRole
              };
            } else if (activeOrgResponse.status === "rejected") {
              // Don't log 401 errors as warnings since they're expected for some auth states
              const error = activeOrgResponse.reason;
              if (error?.message?.includes("401")) {
                console.debug(
                  "User not authorized to fetch active organization (expected during auth flow)"
                );
              } else {
                console.warn("Failed to fetch active organization:", error);
              }
            }
          } catch (error) {
            // Log error but don't fail the entire session check
            console.warn("Unexpected error fetching organization data:", error);
          }
        }

        return sessionData;
      } catch (error) {
        console.error(
          "Error in AuthDAL.getSessionDataWithOrganizations:",
          error
        );
        return null;
      }
    }
  );

  /**
   * Check if user should be redirected from authentication pages
   * Implements the authorization logic for sign-in/sign-up pages
   */
  static async checkAuthPageAccess(): Promise<AuthorizationResult> {
    const sessionData = await this.getSessionData();

    // If no session, user can access auth pages
    if (!sessionData) {
      return {
        isAuthenticated: false,
        shouldRedirect: false
      };
    }

    const { user } = sessionData;

    // User is authenticated, determine where to redirect them
    // We'll fetch organization data only if needed for the redirect logic
    const redirectTo = await this.determineRedirectDestinationForUser(user);

    return {
      isAuthenticated: true,
      shouldRedirect: true,
      redirectTo,
      reason: "User is already authenticated"
    };
  }

  /**
   * Determine the appropriate redirect destination for authenticated users
   * Implements the priority logic specified in requirements
   * This version fetches organization data only when needed
   */
  private static async determineRedirectDestinationForUser(
    user: AuthUserDTO
  ): Promise<string> {
    // Priority 1: If onboarding is not complete, redirect to onboarding
    if (user.onboardingStatus !== "complete") {
      return "/onboarding/workspace";
    }

    // Priority 2: If user has a defaultWorkspace, redirect to it
    if (user.defaultWorkspace) {
      return `/${user.defaultWorkspace}/home`;
    }

    // Priority 3: If email is not verified, redirect to verification
    if (!user.emailVerified) {
      return "/verify";
    }

    // Now we need to check organizations, so fetch the data
    try {
      const sessionDataWithOrgs = await this.getSessionDataWithOrganizations();
      if (sessionDataWithOrgs) {
        const { organizations, activeOrganization } = sessionDataWithOrgs;

        // Priority 4: If user has no organizations, redirect to onboarding
        if (organizations.length === 0) {
          return "/onboarding/workspace";
        }

        // Priority 5: If user has an active organization, redirect to it
        if (activeOrganization) {
          return `/${activeOrganization.slug}/home`;
        }

        // Priority 6: If user has organizations but no active, use the first one
        if (organizations.length > 0) {
          // Use the oldest organization (first in the array as they're ordered by createdAt asc)
          return `/${organizations[0]?.slug}/home`;
        }
      }
    } catch (error) {
      console.warn(
        "Failed to fetch organization data for redirect, using fallback:",
        error
      );
    }

    // Fallback: redirect to root
    return "/";
  }

  /**
   * Utility function to check if user needs email verification
   */
  static async requiresEmailVerification(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData ? !sessionData.user.emailVerified : false;
  }

  /**
   * Utility function to check if user has completed onboarding
   */
  static async hasCompletedOnboarding(): Promise<boolean> {
    const sessionData = await this.getSessionData();
    return sessionData
      ? sessionData.user.onboardingStatus === "complete"
      : false;
  }

  /**
   * Utility function to get user's organizations count
   */
  static async getOrganizationCount(): Promise<number> {
    const sessionData = await this.getSessionDataWithOrganizations();
    return sessionData ? sessionData.organizations.length : 0;
  }
}
